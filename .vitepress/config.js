import lightbox from "vitepress-plugin-lightbox";

export default {
  // 网站标题和描述
  title: "",
  description: "专业的软件开发服务平台 - 提供前端开发、移动应用、技术咨询等一站式数字化解决方案",
  
  // 网站语言
  lang: "zh-CN",
  
  // 部署基础路径
  base: "/",
  
  // 主题配置
  themeConfig: {
    // 网站标题（暂时不使用logo图片）
    // logo: "/images/logo.png",
    siteTitle: false,
    
    // 导航栏配置
    nav: [
      { text: "首页", link: "/" },
      { text: "服务案例", link: "/projects/" },
      { text: "服务能力", link: "/skills" },
    ],
    
    // 侧边栏配置
    sidebar: {
      "/projects/": [
        {
          text: "服务案例",
          items: [
            { text: "所有案例", link: "/projects/" },
            { text: "充电桩开源云平台", link: "/projects/project7" },
            { text: "暹罗点餐系统", link: "/projects/project8" },
            { text: "仓库管理系统", link: "/projects/project9" },
            { text: "校园快递物流管理系统", link: "/projects/project10" },
            { text: "电商管理系统", link: "/projects/project1" },
            { text: "在线图书借阅系统", link: "/projects/project2" },
            { text: "儿童教育网站", link: "/projects/project3" },
            { text: "旅社客房收费管理系统", link: "/projects/project4" },
            { text: "校园线上商城", link: "/projects/project5" },
            { text: "校园帮", link: "/projects/project6" },
          ],
        },
      ],
    },
    
    // 深色模式
    appearance: true,
    
    // 最后更新时间
    lastUpdated: false,
  },
  
  // Markdown 配置
  markdown: {
    lineNumbers: true,
    theme: "github-dark",
    config: (md) => {
      md.use(lightbox);
    }
  },
  
  // 头部标签
  head: [
    ["link", { rel: "icon", href: "/favicon.ico" }],
    ["meta", { name: "author", content: "数字化解决方案团队" }],
    ["meta", { name: "keywords", content: "软件开发,前端开发,移动应用,技术咨询,数字化解决方案" }],
    ["meta", { name: "viewport", content: "width=device-width, initial-scale=1.0" }],
    ["meta", { property: "og:type", content: "website" }],
    ["meta", { property: "og:title", content: "数字化解决方案 - 专业软件开发服务" }],
    ["meta", { property: "og:description", content: "提供前端开发、移动应用、技术咨询等一站式数字化解决方案" }]
  ]
}
