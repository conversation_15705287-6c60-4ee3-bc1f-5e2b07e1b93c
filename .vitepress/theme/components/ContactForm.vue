<template>
  <form class="contact-form" @submit.prevent="handleSubmit">
    <h3>联系我</h3>
    <p>如果您对我的项目感兴趣或有合作意向，欢迎与我联系！</p>
    
    <div class="form-group">
      <label for="name">姓名 *</label>
      <input 
        id="name"
        v-model="form.name" 
        type="text" 
        required 
        placeholder="请输入您的姓名"
      />
    </div>
    
    <div class="form-group">
      <label for="email">邮箱 *</label>
      <input 
        id="email"
        v-model="form.email" 
        type="email" 
        required 
        placeholder="请输入您的邮箱地址"
      />
    </div>
    
    <div class="form-group">
      <label for="subject">主题</label>
      <input 
        id="subject"
        v-model="form.subject" 
        type="text" 
        placeholder="请输入邮件主题"
      />
    </div>
    
    <div class="form-group">
      <label for="message">留言 *</label>
      <textarea 
        id="message"
        v-model="form.message" 
        required 
        placeholder="请输入您的留言内容..."
      ></textarea>
    </div>
    
    <button type="submit" class="submit-btn" :disabled="isSubmitting">
      {{ isSubmitting ? '发送中...' : '发送消息' }}
    </button>
    
    <div v-if="submitMessage" class="submit-message" :class="submitStatus">
      {{ submitMessage }}
    </div>
  </form>
</template>

<script setup>
import { ref, reactive } from 'vue'

const form = reactive({
  name: '',
  email: '',
  subject: '',
  message: ''
})

const isSubmitting = ref(false)
const submitMessage = ref('')
const submitStatus = ref('')

const handleSubmit = async () => {
  isSubmitting.value = true
  submitMessage.value = ''
  
  try {
    // 这里可以集成第三方服务如 Formspree, Netlify Forms 等
    // 目前只是模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟成功
    submitMessage.value = '消息发送成功！我会尽快回复您。'
    submitStatus.value = 'success'
    
    // 重置表单
    Object.keys(form).forEach(key => {
      form[key] = ''
    })
    
  } catch (error) {
    submitMessage.value = '发送失败，请稍后重试或直接发送邮件联系我。'
    submitStatus.value = 'error'
  } finally {
    isSubmitting.value = false
    
    // 3秒后清除消息
    setTimeout(() => {
      submitMessage.value = ''
    }, 3000)
  }
}
</script>

<style scoped>
.contact-form h3 {
  color: var(--portfolio-primary);
  margin-bottom: var(--portfolio-spacing-sm);
  font-size: 1.5rem;
  font-weight: 600;
}

.contact-form > p {
  color: var(--vp-c-text-2);
  margin-bottom: var(--portfolio-spacing-lg);
  line-height: 1.6;
}

.submit-message {
  margin-top: var(--portfolio-spacing-md);
  padding: var(--portfolio-spacing-sm);
  border-radius: var(--portfolio-radius-md);
  font-weight: 500;
}

.submit-message.success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--portfolio-success);
  border: 1px solid var(--portfolio-success);
}

.submit-message.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--portfolio-error);
  border: 1px solid var(--portfolio-error);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
