<template>
  <div class="project-card fade-in-up">
    <div v-if="image" class="project-image">
      <img :src="image" :alt="title" />
    </div>

    <!-- 当没有图片时显示图标占位 -->
    <div v-else class="project-placeholder">
      <div class="placeholder-icon">
        {{ getProjectIcon(title) }}
      </div>
      <div class="placeholder-text">{{ title }}</div>
    </div>

    <div class="project-content">
      <h3>{{ title }}</h3>
      <p>{{ description }}</p>

      <div class="project-tech">
        <span v-for="tech in technologies" :key="tech" class="tech-tag">
          {{ tech }}
        </span>
      </div>

      <div class="project-links">
        <a v-if="detailUrl" :href="detailUrl" class="project-link project-link-primary">
          <span>详细介绍</span>
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  technologies: {
    type: Array,
    default: () => []
  },
  image: {
    type: String,
    default: ''
  },
  demoUrl: {
    type: String,
    default: ''
  },
  githubUrl: {
    type: String,
    default: ''
  },
  detailUrl: {
    type: String,
    default: ''
  }
})

// 根据项目标题返回对应的图标
const getProjectIcon = (title) => {
  const iconMap = {
    '电商': '🛒',
    '学习': '📚',
    '任务': '✅',
    '博客': '📝',
    '数据': '📊',
    '小程序': '📱',
    '管理': '⚙️',
    '平台': '🌐'
  }

  for (const [key, icon] of Object.entries(iconMap)) {
    if (title.includes(key)) {
      return icon
    }
  }

  return '💻' // 默认图标
}
</script>

<style scoped>
.project-image {
  margin-bottom: 1rem;
  border-radius: var(--portfolio-radius-md);
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

.project-placeholder {
  margin-bottom: 1rem;
  height: 200px;
  background: linear-gradient(135deg, var(--portfolio-primary), var(--portfolio-accent));
  border-radius: var(--portfolio-radius-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  transition: transform 0.3s ease;
}

.project-card:hover .project-placeholder {
  transform: scale(1.02);
}

.placeholder-icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.placeholder-text {
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
}

.project-content {
  flex: 1;
}
</style>
