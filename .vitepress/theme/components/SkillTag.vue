<template>
  <span 
    class="skill-tag" 
    :class="{ 
      'skill-tag--primary': level === 'expert',
      'skill-tag--secondary': level === 'intermediate',
      'skill-tag--basic': level === 'basic'
    }"
  >
    <span v-if="icon" class="skill-icon">{{ icon }}</span>
    <span class="skill-name">{{ name }}</span>
    <span v-if="showLevel" class="skill-level">{{ levelText }}</span>
  </span>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  name: {
    type: String,
    required: true
  },
  level: {
    type: String,
    default: 'intermediate',
    validator: (value) => ['basic', 'intermediate', 'expert'].includes(value)
  },
  icon: {
    type: String,
    default: ''
  },
  showLevel: {
    type: Boolean,
    default: false
  }
})

const levelText = computed(() => {
  const levelMap = {
    basic: '基础',
    intermediate: '熟练',
    expert: '精通'
  }
  return levelMap[props.level]
})
</script>

<style scoped>
.skill-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 1rem;
  border-radius: var(--portfolio-radius-md);
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  cursor: default;
}

.skill-tag--primary {
  background: var(--portfolio-primary);
  color: white;
}

.skill-tag--secondary {
  background: var(--portfolio-secondary);
  color: white;
}

.skill-tag--basic {
  background: var(--vp-c-bg-soft);
  color: var(--vp-c-text-2);
  border: 1px solid var(--vp-c-divider);
}

.skill-icon {
  font-size: 1rem;
}

.skill-level {
  font-size: 0.75rem;
  opacity: 0.8;
}

.skill-tag:hover {
  transform: translateY(-2px);
  box-shadow: var(--portfolio-shadow-md);
}
</style>
