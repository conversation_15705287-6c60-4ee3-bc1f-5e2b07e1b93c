/* 自定义主题样式 */

/* 全局变量 */
:root {
  --vp-c-brand-1: #646cff;
  --vp-c-brand-2: #747bff;
  --vp-c-brand-3: #9499ff;
  --vp-c-brand-soft: rgba(100, 108, 255, 0.14);
  
  /* 自定义颜色 */
  --portfolio-primary: #2563eb;
  --portfolio-secondary: #64748b;
  --portfolio-accent: #f59e0b;
  --portfolio-success: #10b981;
  --portfolio-warning: #f59e0b;
  --portfolio-error: #ef4444;
  
  /* 间距 */
  --portfolio-spacing-xs: 0.5rem;
  --portfolio-spacing-sm: 1rem;
  --portfolio-spacing-md: 1.5rem;
  --portfolio-spacing-lg: 2rem;
  --portfolio-spacing-xl: 3rem;
  
  /* 阴影 */
  --portfolio-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --portfolio-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --portfolio-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  
  /* 圆角 */
  --portfolio-radius-sm: 0.375rem;
  --portfolio-radius-md: 0.5rem;
  --portfolio-radius-lg: 0.75rem;
}

/* 深色模式变量 */
.dark {
  --portfolio-primary: #3b82f6;
  --portfolio-secondary: #94a3b8;
}

/* 首页样式 */
.VPHome {
  padding-bottom: 0;
}

.VPHero {
  padding-top: 120px !important;
  padding-bottom: 80px !important;
}

.VPHero .name {
  background: linear-gradient(120deg, var(--portfolio-primary) 30%, var(--portfolio-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.VPHero .text {
  font-size: 1.25rem;
  line-height: 1.6;
  color: var(--vp-c-text-2);
  margin-top: 1rem;
}

/* 项目卡片样式 */
.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--portfolio-spacing-lg);
  margin: var(--portfolio-spacing-xl) 0;
}

.project-card {
  background: var(--vp-c-bg-soft);
  border: 1px solid var(--vp-c-divider);
  border-radius: var(--portfolio-radius-lg);
  padding: var(--portfolio-spacing-lg);
  transition: all 0.3s ease;
  box-shadow: var(--portfolio-shadow-sm);
}

.project-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--portfolio-shadow-lg);
  border-color: var(--portfolio-primary);
}

.project-card h3 {
  color: var(--portfolio-primary);
  margin-bottom: var(--portfolio-spacing-sm);
  font-size: 1.25rem;
  font-weight: 600;
}

.project-card p {
  color: var(--vp-c-text-2);
  line-height: 1.6;
  margin-bottom: var(--portfolio-spacing-md);
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: var(--portfolio-spacing-xs);
  margin-bottom: var(--portfolio-spacing-md);
}

.tech-tag {
  background: var(--portfolio-primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: var(--portfolio-radius-sm);
  font-size: 0.875rem;
  font-weight: 500;
}

.project-links {
  display: flex;
  gap: var(--portfolio-spacing-sm);
}

.project-link {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background: var(--portfolio-primary);
  color: white;
  text-decoration: none;
  border-radius: var(--portfolio-radius-md);
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.project-link:hover {
  background: var(--portfolio-accent);
  color: white;
}

.project-link-primary {
  background-color: var(--portfolio-primary);
  color: white;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  text-align: center;
  width: 100%;
  justify-content: center;
  border: 1px solid transparent; /* 添加透明边框以防止悬停时跳动 */
}

.project-link-primary:hover {
  background-color: white;
  color: var(--portfolio-primary);
  border: 1px solid var(--portfolio-primary);
  transform: translateY(-2px);
  box-shadow: var(--portfolio-shadow-md);
}

/* 技能标签样式 */
.skills-section {
  margin: var(--portfolio-spacing-xl) 0;
}

.skills-category {
  margin-bottom: var(--portfolio-spacing-lg);
}

.skills-category h3 {
  color: var(--portfolio-primary);
  margin-bottom: var(--portfolio-spacing-md);
  font-size: 1.125rem;
  font-weight: 600;
}

.skills-grid {
  display: flex;
  flex-wrap: wrap;
  gap: var(--portfolio-spacing-sm);
}

.skill-tag {
  background: var(--vp-c-bg-soft);
  border: 1px solid var(--vp-c-divider);
  color: var(--vp-c-text-1);
  padding: 0.5rem 1rem;
  border-radius: var(--portfolio-radius-md);
  font-weight: 500;
  transition: all 0.3s ease;
}

.skill-tag:hover {
  background: var(--portfolio-primary);
  color: white;
  border-color: var(--portfolio-primary);
}

/* 联系表单样式 */
.contact-form {
  max-width: 600px;
  margin: var(--portfolio-spacing-xl) auto;
  padding: var(--portfolio-spacing-lg);
  background: var(--vp-c-bg-soft);
  border-radius: var(--portfolio-radius-lg);
  border: 1px solid var(--vp-c-divider);
}

.form-group {
  margin-bottom: var(--portfolio-spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--portfolio-spacing-xs);
  font-weight: 500;
  color: var(--vp-c-text-1);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--vp-c-divider);
  border-radius: var(--portfolio-radius-md);
  background: var(--vp-c-bg);
  color: var(--vp-c-text-1);
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--portfolio-primary);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-btn {
  background: var(--portfolio-primary);
  color: white;
  padding: 0.75rem 2rem;
  border: none;
  border-radius: var(--portfolio-radius-md);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.submit-btn:hover {
  background: var(--portfolio-accent);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .project-grid {
    grid-template-columns: 1fr;
    gap: var(--portfolio-spacing-md);
  }
  
  .VPHero {
    padding-top: 80px !important;
    padding-bottom: 60px !important;
  }
  
  .project-links {
    flex-direction: column;
  }
  
  .skills-grid {
    gap: var(--portfolio-spacing-xs);
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--vp-c-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--vp-c-divider);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--portfolio-primary);
}
