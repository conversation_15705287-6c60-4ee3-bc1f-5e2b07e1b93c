# 个人作品集门户网站

这是一个基于 VitePress 构建的个人作品集网站，用于展示计算机软件专业学生的项目案例、技能专长和个人经历。

## 🌟 项目特色

- **现代化设计**: 采用简洁优雅的设计风格，支持深色/浅色主题切换
- **响应式布局**: 完美适配桌面端、平板和移动设备
- **项目展示**: 专业的项目案例展示，包含技术栈、演示链接等
- **技能可视化**: 直观的技能标签和等级展示
- **SEO 优化**: 良好的搜索引擎优化，提升网站可见性
- **快速加载**: 基于 Vite 构建，具有出色的性能表现

## 🛠️ 技术栈

- **框架**: VitePress (基于 Vue.js 和 Vite)
- **样式**: CSS3 + 自定义主题
- **组件**: Vue 3 组件化开发
- **构建工具**: Vite
- **部署**: 支持静态网站部署

## 🚀 快速开始

### 环境要求

- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器

### 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 开发模式

```bash
# 启动开发服务器
npm run dev

# 或
yarn dev
```

访问 `http://localhost:5173` 查看网站。

### 构建生产版本

```bash
# 构建静态文件
npm run build

# 或
yarn build
```

构建完成后，静态文件将生成在 `.vitepress/dist` 目录中。