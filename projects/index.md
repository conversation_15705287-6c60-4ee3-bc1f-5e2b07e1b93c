# 服务案例

这里展示了我们为客户提供的优质服务案例，涵盖了前端开发、全栈应用、移动端开发等多个领域。每个案例都体现了我们在不同行业和技术栈上的专业能力。

## 🌟 精选案例

<div class="project-grid">

<ProjectCard
  title="充电桩开源云平台"
  description="一个功能强大的充电桩运营管理解决方案，基于微服务架构构建，提供高并发、高可用的商业级充电服务。"
  :technologies="['Spring Cloud', 'Vue.js', 'MyBatis-Plus', 'MySQL', 'Redis', 'RabbitMQ']"
  image="/images/charge-cloud/2.png"
  detail-url="/projects/project7"
/>

<ProjectCard
  title="暹罗点餐系统"
  description="一套完整的、商业级的餐饮解决方案，涵盖用户端、商户端和管理后台，支持外卖、堂食等多种模式。"
  :technologies="['Spring Boot', 'Vue.js', '微信小程序', 'MySQL', 'Redis']"
  image="/images/siam-server/home.jpg"
  detail-url="/projects/project8"
/>

<ProjectCard
  title="电商管理系统"
  description="企业级电商后台管理解决方案，采用 Spring Boot + Vue.js 前后端分离架构，深度整合商品、订单、用户及数据分析等核心模块，全面赋能业务运营。"
  :technologies="['Spring Boot', 'Vue.js', 'MyBatis', 'MySQL', 'Redis', 'Element Plus']"
  image="/images/Mall-backend-management-system/e.png"
  detail-url="/projects/project1"
/>

<ProjectCard
  title="在线图书借阅系统"
  description="一套完整的数字化图书馆管理解决方案，旨在简化借阅流程、提升读者体验，并为管理员提供高效的后台管理工具。"
  :technologies="['Django', 'Vue.js', 'Django REST Framework', 'MySQL', 'Redis', 'Element Plus']"
  image="/images/Online-book-lending-system/a.png"
  detail-url="/projects/project2"
/>

<ProjectCard
  title="儿童教育网站"
  description="一个旨在打造沉浸式、个性化学习体验的综合性在线教育解决方案，通过 Uniapp 实现跨平台无缝学习。"
  :technologies="['Spring Boot', 'Vue.js', 'Uniapp', 'MyBatis-Plus', 'MySQL', 'Redis']"
  image="/images/Children-education-website/a.png"
  detail-url="/projects/project3"
/>

<ProjectCard
  title="旅社客房收费管理系统"
  description="为现代旅宿行业打造的数字化运营管理平台，通过 Uniapp 实现多端预订，全面提升运营效率与客户满意度。"
  :technologies="['Spring Boot', 'Vue.js', 'Uniapp', 'MyBatis-Plus', 'MySQL', 'Redis']"
  image="/images/Travel-agency-room-fee-management-system/a.png"
  detail-url="/projects/project4"
/>

<ProjectCard
  title="校园线上商城"
  description="专为高校场景设计的一站式校园生活服务电商平台，旨在为在校师生提供便捷、安全的在线购物体验。"
  :technologies="['Spring Boot', 'SSM', 'Vue.js', 'MyBatis', 'MySQL', 'Redis']"
  image="/images/Online-school-shop/a.png"
  detail-url="/projects/project5"
/>

<ProjectCard
  title="校园帮"
  description="P2P（点对点）校园服务共享与协作平台，旨在构建一个互助的“零工经济”社区，促进资源与技能交换。"
  :technologies="['Spring', 'MyBatis', 'JSP', 'MySQL', 'Tomcat', 'Bootstrap']"
  image="/images/campus-help/a.png"
  detail-url="/projects/project6"
/>

</div>

## 📊 服务数据

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem; margin: 2rem 0;">

<div style="text-align: center; padding: 1.5rem; background: var(--vp-c-bg-soft); border-radius: 0.75rem; border: 1px solid var(--vp-c-divider);">
  <div style="font-size: 2rem; font-weight: bold; color: var(--portfolio-primary);">3000+</div>
  <div style="color: var(--vp-c-text-2);">服务客户</div>
</div>

<div style="text-align: center; padding: 1.5rem; background: var(--vp-c-bg-soft); border-radius: 0.75rem; border: 1px solid var(--vp-c-divider);">
  <div style="font-size: 2rem; font-weight: bold; color: var(--portfolio-primary);">2000+</div>
  <div style="color: var(--vp-c-text-2);">完成项目</div>
</div>

<div style="text-align: center; padding: 1.5rem; background: var(--vp-c-bg-soft); border-radius: 0.75rem; border: 1px solid var(--vp-c-divider);">
  <div style="font-size: 2rem; font-weight: bold; color: var(--portfolio-primary);">100%</div>
  <div style="color: var(--vp-c-text-2);">客户满意度</div>
</div>

<div style="text-align: center; padding: 1.5rem; background: var(--vp-c-bg-soft); border-radius: 0.75rem; border: 1px solid var(--vp-c-divider);">
  <div style="font-size: 2rem; font-weight: bold; color: var(--portfolio-primary);">100%</div>
  <div style="color: var(--vp-c-text-2);">按时交付</div>
</div>

</div>

## 🛠️ 服务技术栈

我们在项目开发中使用的主要技术栈：

### 前端技术
- **框架**: Vue.js (70%), React (25%), Angular (5%)
- **样式**: CSS3/Sass (40%), Tailwind CSS (35%), UI 组件库 (25%)
- **构建工具**: Vite (50%), Webpack (30%), Rollup (20%)

### 后端技术
- **语言**: Node.js (60%), Python (25%), Java (15%)
- **数据库**: MongoDB (45%), MySQL (35%), PostgreSQL (20%)
- **框架**: Express (40%), Koa (30%), Spring Boot (30%)

### 移动端开发
- **跨平台**: Vue/React Native (60%), 微信小程序 (40%)
- **原生**: Android (基础), iOS (基础)

---

<div style="text-align: center; margin: 3rem 0;">
  <h2>🚀 专业服务，品质保证</h2>
  <p style="color: var(--vp-c-text-2); font-size: 1.1rem; max-width: 600px; margin: 0 auto;">
    每个案例都是我们专业能力的体现，我们始终坚持高标准的服务质量，为客户提供最优质的数字化解决方案。
  </p>
</div>
