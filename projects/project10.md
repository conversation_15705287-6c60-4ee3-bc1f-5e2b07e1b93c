# 校园快递物流管理系统

<div style="text-align: center; margin-bottom: 2rem;">
  <img src="/images/express/2024-04-17 12_36_55_Snipaste_2024-04-17_12-25-23.png" alt="校园快递物流管理系统预览" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
</div>

## 项目概述

“校园快递物流管理系统”是一个专为高校设计的快递服务解决方案，旨在解决校园内快递“最后一公里”的派送与管理难题。系统连接了学生、快递员和管理员，通过线上平台简化了取件、派件和信息查询流程，提升了校园物流的整体效率和用户体验。

## 功能描述

系统包含普通用户和管理员两种角色：

### 管理员功能
- **权限管理**: 登录、注册、管理员管理与添加。
- **用户管理**: 快递用户管理与添加、用户信息管理与添加。
- **业务管理**: 快递订单信息管理、代取件信息管理。
- **内容管理**: 公告信息管理与添加。
- **系统操作**: 安全退出。

### 普通用户功能
- 登录注册、查看快递、在线下单、查询物流、管理个人信息等。

## 相关技术

- **后端**: Java, Spring, Spring MVC, MyBatis, JDK 1.8
- **前端**: Vue, HTML, CSS, JavaScript
- **数据库**: MySQL

## 开发软件

- **IDE**: IDEA, Eclipse, Visual Studio Code (VSCode)
- **数据库工具**: Navicat

## 系统截图

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin: 2rem 0;">
  <img src="/images/express/2024-04-17 12_37_03_Snipaste_2024-04-17_12-17-54.png" alt="截图1" style="width: 100%; border-radius: 8px;">
  <img src="/images/express/2024-04-17 12_37_06_Snipaste_2024-04-17_12-18-02.png" alt="截图2" style="width: 100%; border-radius: 8px;">
  <img src="/images/express/2024-04-17 12_37_10_Snipaste_2024-04-17_12-18-11.png" alt="截图3" style="width: 100%; border-radius: 8px;">
  <img src="/images/express/2024-04-17 12_37_14_Snipaste_2024-04-17_12-18-18.png" alt="截图4" style="width: 100%; border-radius: 8px;">
  <img src="/images/express/2024-04-17 12_37_18_Snipaste_2024-04-17_12-18-28.png" alt="截图5" style="width: 100%; border-radius: 8px;">
  <img src="/images/express/2024-04-17 12_37_28_Snipaste_2024-04-17_12-18-47.png" alt="截图6" style="width: 100%; border-radius: 8px;">
</div>
