# 在线图书借阅系统

<div style="text-align: center; margin-bottom: 2rem;">
  <img src="/images/Online-book-lending-system/a.png" alt="在线图书借阅系统预览" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
</div>

## 项目概述

在线图书借阅系统是一套完整的数字化图书馆管理解决方案。项目基于现代B/S架构和前后端分离模式开发，旨在通过线上平台简化图书借阅流程、提升读者服务体验，并为管理员提供高效的后台管理工具。系统无缝集成了用户端借阅门户与管理员端控制台，实现了图书资源的全周期数字化管理。

## 功能模块

### 前台功能
- **首页**：展示推荐图书、最新上架等信息。
- **图书详情页**：显示图书的详细信息、库存、评论等。
- **用户中心模块**：管理个人信息、查看借阅历史、处理归还等。

### 后台功能
- **总览**：系统核心数据统计与可视化展示。
- **借阅管理**：处理借阅请求、跟踪借阅状态。
- **图书管理**：添加、编辑、删除图书信息。
- **分类管理**：管理图书分类。
- **标签管理**：为图书添加和管理标签。
- **评论管理**：审核和管理用户评论。
- **用户管理**：管理平台用户信息和权限。
- **运营管理**：配置网站公告、活动等。
- **日志管理**：记录系统操作日志。
- **系统信息**：查看系统运行状态和基本信息。

## 技术栈

### 前端技术
- **框架**: Vue.js 3.x
- **UI 组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4.x
- **HTTP 请求**: Axios
- **构建工具**: Vite

### 后端技术
- **框架**: Django
- **API 框架**: Django REST Framework
- **数据库**: MySQL
- **缓存**: Redis
- **身份认证**: JWT (JSON Web Token)

## 系统截图

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin: 2rem 0;">
  <img src="/images/Online-book-lending-system/b.png" alt="后台截图1" style="width: 100%; border-radius: 8px;">
  <img src="/images/Online-book-lending-system/c.png" alt="后台截图2" style="width: 100%; border-radius: 8px;">
  <img src="/images/Online-book-lending-system/d.png" alt="前台截图1" style="width: 100%; border-radius: 8px;">
  <img src="/images/Online-book-lending-system/e.png" alt="前台截图2" style="width: 100%; border-radius: 8px;">
  <img src="/images/Online-book-lending-system/f.png" alt="前台截图3" style="width: 100%; border-radius: 8px;">
</div>
