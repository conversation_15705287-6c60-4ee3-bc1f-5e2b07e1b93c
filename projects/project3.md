# 儿童教育网站

<div style="text-align: center; margin-bottom: 2rem;">
  <img src="/images/Children-education-website/a.png" alt="儿童教育网站预览" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
</div>

## 项目概述

儿童在线教育平台是一个旨在打造沉浸式、个性化学习体验的综合性解决方案。项目采用 Spring Boot + Vue.js + Uniapp 技术栈，构建了功能丰富的 Web 端和移动端（小程序/App）应用，实现了跨平台学习的无缝对接。平台不仅为学员提供了互动式的在线课程，还为教育机构提供了强大的后台管理系统，从而构建了一个集教学、管理、互动于一体的现代化教育生态。

## 功能模块

### 前台功能
- **课程中心**：展示所有在线课程，支持分类和搜索。
- **在线学习**：提供视频、图文等多种形式的课程内容。
- **学习社区**：学员可以在社区中提问、交流、分享学习心得。
- **个人中心**：管理个人资料、查看学习进度和课程表。

### 后台功能
- **课程管理**：发布、编辑和管理所有课程信息。
- **学员管理**：查看学员信息、管理学员状态。
- **教师管理**：管理教师信息和授课安排。
- **订单管理**：处理课程购买订单。
- **数据统计**：分析平台运营数据，如学员增长、课程热度等。

## 技术栈

### 前端技术
- **核心框架**: Vue.js
- **跨端框架**: Uniapp (支持小程序、H5、App)
- **UI 组件库**: Element Plus (后台), Vant (移动端)
- **状态管理**: Vuex / Pinia
- **路由管理**: Vue Router
- **HTTP 通信**: Axios

### 后端技术
- **核心框架**: Spring Boot
- **持久层框架**: MyBatis-Plus
- **安全认证**: Spring Security + JWT
- **数据库**: MySQL
- **缓存技术**: Redis

## 系统截图

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin: 2rem 0;">
  <img src="/images/Children-education-website/b.png" alt="截图1" style="width: 100%; border-radius: 8px;">
  <img src="/images/Children-education-website/c.png" alt="截图2" style="width: 100%; border-radius: 8px;">
  <img src="/images/Children-education-website/d.png" alt="截图3" style="width: 100%; border-radius: 8px;">
  <img src="/images/Children-education-website/e.png" alt="截图4" style="width: 100%; border-radius: 8px;">
</div>
