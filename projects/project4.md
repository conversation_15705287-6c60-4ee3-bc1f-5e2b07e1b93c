# 旅社客房收费管理系统

<div style="text-align: center; margin-bottom: 2rem;">
  <img src="/images/Travel-agency-room-fee-management-system/a.png" alt="旅社客房收费管理系统预览" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
</div>

## 项目概述

本项目是为现代旅宿行业（如旅社、民宿、精品酒店）量身打造的一款数字化运营管理平台。系统采用 Spring Boot + Vue.js + Uniapp 架构，通过 Uniapp 技术构建了面向顾客的多端预订应用（小程序/H5），同时为管理者提供了功能强大的后台管理系统。平台旨在打通前后端业务链路，优化客房资源配置，简化预订与收费流程，从而全面提升运营效率与客户满意度。

## 功能模块

### 前台功能
- **客房预订**: 用户可以浏览客房信息并进行在线预订。
- **订单查询**: 用户可以查询自己的预订订单状态。
- **在线支付**: 支持多种支付方式完成订单支付。
- **个人中心**: 管理个人信息和历史订单。

### 后台功能
- **客房管理**: 管理客房类型、价格、状态等信息。
- **订单管理**: 处理和查看所有客房预订订单。
- **用户管理**: 管理注册用户信息。
- **财务统计**: 对收入进行统计和分析。

## 技术栈

### 前端技术
- **核心框架**: Vue.js
- **跨端框架**: Uniapp (实现小程序/H5等多端覆盖)
- **UI 组件库**: Element Plus (后台管理), uView (移动端)
- **状态管理**: Pinia
- **HTTP 通信**: Axios

### 后端技术
- **核心框架**: Spring Boot
- **持久层框架**: MyBatis-Plus
- **安全认证**: Spring Security + JWT
- **数据库**: MySQL
- **缓存技术**: Redis

## 系统截图

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin: 2rem 0;">
  <img src="/images/Travel-agency-room-fee-management-system/b.png" alt="截图1" style="width: 100%; border-radius: 8px;">
  <img src="/images/Travel-agency-room-fee-management-system/c.png" alt="截图2" style="width: 100%; border-radius: 8px;">
  <img src="/images/Travel-agency-room-fee-management-system/d.png" alt="截图3" style="width: 100%; border-radius: 8px;">
  <img src="/images/Travel-agency-room-fee-management-system/e.png" alt="截图4" style="width: 100%; border-radius: 8px;">
</div>
