# 校园线上商城

<div style="text-align: center; margin-bottom: 2rem;">
  <img src="/images/Online-school-shop/a.png" alt="校园线上商城预览" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
</div>

## 项目概述

校园线上商城是专为高校场景设计的一站式校园生活服务电商平台。项目后端基于 Spring Boot 框架，整合了 Spring、Spring MVC 及 MyBatis (SSM) 的经典分层架构，前端则采用 Vue.js 构建动态用户界面。平台旨在为在校师生提供便捷、安全的在线购物体验，打通商品浏览、购物车、在线支付到订单管理的全链路，构建一个活跃的校园数字消费生态。

## 功能模块

### 前台功能
- **用户注册/登录**：提供安全的账户管理体系。
- **商品浏览与搜索**：支持按分类浏览和关键词搜索商品。
- **购物车管理**：方便地添加、删除和修改购物车中的商品。
- **在线下单与支付**：流畅的下单流程和支付体验。
- **个人中心**：管理个人信息、查看订单历史。

### 后台功能
- **商品管理**：对商品信息进行增、删、改、查。
- **订单管理**：集中处理和跟踪所有用户订单。
- **用户管理**：管理平台所有注册用户信息。
- **数据统计**：提供销售数据和用户行为分析。

## 技术栈

### 前端技术
- **核心框架**: Vue.js
- **UI 组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **HTTP 通信**: Axios

### 后端技术
- **核心框架**: Spring Boot
- **Web 框架**: Spring MVC
- **持久层框架**: MyBatis
- **安全认证**: Spring Security + JWT
- **数据库**: MySQL
- **缓存技术**: Redis

## 系统截图

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin: 2rem 0;">
  <img src="/images/Online-school-shop/b.png" alt="截图1" style="width: 100%; border-radius: 8px;">
  <img src="/images/Online-school-shop/c.png" alt="截图2" style="width: 100%; border-radius: 8px;">
  <img src="/images/Online-school-shop/d.png" alt="截图3" style="width: 100%; border-radius: 8px;">
  <img src="/images/Online-school-shop/e.png" alt="截图4" style="width: 100%; border-radius: 8px;">
  <img src="/images/Online-school-shop/f.png" alt="截图5" style="width: 100%; border-radius: 8px;">
  <img src="/images/Online-school-shop/g.png" alt="截图6" style="width: 100%; border-radius: 8px;">
</div>
