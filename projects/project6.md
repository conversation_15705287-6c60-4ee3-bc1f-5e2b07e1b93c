# 校园帮

<div style="text-align: center; margin-bottom: 2rem;">
  <img src="/images/campus-help/a.png" alt="校园帮预览" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
</div>

## 项目概述

“校园帮”是一个P2P（点对点）校园服务共享与协作平台，旨在构建一个充满活力和互助精神的学生社区。项目采用经典的 Java Web 技术栈，让学生可以便捷地发布服务请求（如代取快递、学业辅导、活动协助等），同时其他学生也能接受这些“零工”任务，以发挥自己的技能和时间价值。平台致力于在校园内创建一个微型“零工经济”，有效促进资源共享、技能交换与互助文化。

## 功能模块

### 用户端功能
- **注册与登录**：保障用户账户安全。
- **公告中心**：查看平台发布的最新公告和信息。
- **任务管理**：自由发布、接受和完成各类校园任务。
- **个人中心**：管理个人信息、充值、查看任务历史。
- **评价管理**：对完成的任务进行评价或查看他人评价。

### 管理员端功能
- **公告管理**：发布和管理平台公告。
- **任务管理**：对平台上的任务进行审核、下架或删除。
- **用户管理**：管理平台用户信息，包括充值和权限控制。

## 技术栈

### 前端技术
- **页面技术**: JSP (JavaServer Pages) + JSTL
- **UI 框架**: Bootstrap
- **交互技术**: jQuery, AJAX

### 后端技术
- **核心框架**: Spring + Spring MVC
- **持久层框架**: MyBatis
- **数据库**: MySQL
- **Web 服务器**: Apache Tomcat

## 系统截图

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin: 2rem 0;">
  <img src="/images/campus-help/b.png" alt="截图1" style="width: 100%; border-radius: 8px;">
  <img src="/images/campus-help/c.png" alt="截图2" style="width: 100%; border-radius: 8px;">
  <img src="/images/campus-help/d.png" alt="截图3" style="width: 100%; border-radius: 8px;">
  <img src="/images/campus-help/e.png" alt="截图4" style="width: 100%; border-radius: 8px;">
  <img src="/images/campus-help/f.png" alt="截图5" style="width: 100%; border-radius: 8px;">
</div>
