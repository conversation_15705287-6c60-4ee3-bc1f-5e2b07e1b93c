# 充电桩开源云平台

<div style="text-align: center; margin-bottom: 2rem;">
  <img src="/images/charge-cloud/2.png" alt="充电桩管理系统预览" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
</div>

## 项目概述

“充电桩开源云平台”是一个功能强大的充电桩运营管理解决方案，基于微服务架构（Spring Cloud）构建，旨在提供一个高并发、高可用、可动态伸缩的商业级充电服务平台。该项目支持多种充电协议（如中电联互联互通协议）和第三方平台对接，实现了设备监控、分时电价、多租户管理等核心功能。项目包含了完整的后台管理系统、用户小程序以及独特的“模拟桩”模块，方便开发者进行快速部署、测试和二次开发。

## 核心功能

- **多协议与平台支持**：支持中电联互联互通协议，可对接特来电、快电等第三方平台及市政平台。
- **高可用微服务架构**：采用 Spring Cloud 技术栈，业务模块独立，支持动态伸缩与无感升级。
- **全面的监控与管理**：提供充电桩实时监控、故障预警、订单管理、财务统计等功能。
- **多租户支持**：基于 RuoYi 框架，提供完善的权限与多租户管理体系。
- **模拟与调试**：内置“模拟桩”模块，可仿真充电过程，极大地方便了业务调试与开发。
- **前后端分离**：后端采用 Spring Cloud，管理端采用 Vue，移动端采用 uni-app，职责清晰，易于维护。

## 技术栈

### 前端技术
- **管理后台**: Vue + Element-UI
- **移动端**: uni-app

### 后端技术
- **核心框架**: Spring Cloud, Spring Boot
- **持久层框架**: MyBatis-Plus
- **数据库**: MySQL, Redis
- **消息队列**: RabbitMQ
- **服务注册与发现**: Nacos
- **通信框架**: Smart-Socket

## 系统截图

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin: 2rem 0;">
  <img src="/images/charge-cloud/1.png" alt="截图1" style="width: 100%; border-radius: 8px;">
  <img src="/images/charge-cloud/4.png" alt="截图2" style="width: 100%; border-radius: 8px;">
  <img src="/images/charge-cloud/5.png" alt="截图3" style="width: 100%; border-radius: 8px;">
  <img src="/images/charge-cloud/8.png" alt="截图4" style="width: 100%; border-radius: 8px;">
  <img src="/images/charge-cloud/3.png" alt="截图5" style="width: 100%; border-radius: 8px;">
  <img src="/images/charge-cloud/7.png" alt="截图6" style="width: 100%; border-radius: 8px;">
</div>
