# 暹罗点餐系统 (Siam Server)

<div style="text-align: center; margin-bottom: 2rem;">
  <img src="/images/siam-server/home.jpg" alt="点餐系统预览" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
</div>

## 项目概述

“暹罗点餐系统”是一套完整的、商业级的餐饮解决方案，旨在为餐厅和用户提供高效、便捷的数字化服务。项目采用前后端分离架构，涵盖了用户点餐小程序、商户管理后台和平台管理后台，支持外卖、自取、堂食等多种业务模式。系统功能全面，从菜品管理、订单处理到会员营销、数据统计，为商户提供了强大的运营支持。

## 功能模块

### 用户端 (微信小程序)
- **在线点餐**：支持外卖、预约、堂食扫码点餐。
- **订单管理**：查看订单状态、申请退款、评价订单。
- **会员中心**：管理个人信息、查看会员等级、积分与优惠券。
- **营销功能**：参与积分商城、领取优惠券、邀请好友获得奖励。

### 商户与管理后台 (Vue)
- **商品管理**：菜品上下架、分类管理、库存设置。
- **订单处理**：自动接单、订单核销、退款审批。
- **店铺管理**：设置营业时间、配送范围、打印机管理。
- **营销推广**：创建优惠券、管理会员体系、查看营销数据。
- **数据统计**：提供营业额、订单量、热门商品等多维度数据分析。

## 技术栈

- **后端技术**: Java, Spring Boot
- **前端技术**: Vue.js (管理端), 微信小程序 (用户端)
- **数据库**: MySQL, Redis
- **开发工具**: Maven, Git

## 系统截图

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin: 2rem 0;">
  <img src="/images/siam-server/order_takeout.jpg" alt="截图1" style="width: 100%; border-radius: 8px;">
  <img src="/images/siam-server/shop.jpg" alt="截图2" style="width: 100%; border-radius: 8px;">
  <img src="/images/siam-server/order_list.jpg" alt="截图3" style="width: 100%; border-radius: 8px;">
  <img src="/images/siam-server/coupons.jpg" alt="截图4" style="width: 100%; border-radius: 8px;">
  <img src="/images/siam-server/refundOrderList.png" alt="截图5" style="width: 100%; border-radius: 8px;">
  <img src="/images/siam-server/memberList.png" alt="截图6" style="width: 100%; border-radius: 8px;">
</div>
