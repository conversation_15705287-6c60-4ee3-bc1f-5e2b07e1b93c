# 仓库管理系统 (Warehouse Management System)

<div style="text-align: center; margin-bottom: 2rem;">
  <img src="/images/warehouse/2024-12-09 19_12_42_image.png" alt="仓库管理系统预览" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
</div>

## 项目概述

“仓库管理系统”是一个功能强大的企业级仓储解决方案，旨在通过数字化和自动化手段，提升仓库运营效率、降低管理成本。该系统基于Java技术栈构建，提供了从入库、出库、库存管理到数据分析的全方位功能，适用于各类制造、零售和物流企业。系统角色主要为管理员，负责整个仓库的运营与监控。

## 功能描述

系统核心功能均由管理员操作，具体包括：

- **系统管理**: 登录、用户管理、角色列表、权限列表、分配角色。
- **监控与分析**: SQL监控、数据报表、客户列表。
- **核心业务**:
    - **生产管理**: 我的生产、生产档案。
    - **仓储管理**: 我的仓库、入库管理、出库管理。
    - **订单管理**: 订单列表。

## 相关技术

- **前端**: Vue, HTML, CSS, JavaScript
- **后端**: Spring Boot
- **数据库**: MySQL 5.7+
- **缓存**: Redis 3.0+

## 开发与部署

- **开发软件**: IDEA, Eclipse, Visual Studio Code (VSCode), Navicat
- **部署环境**:
    - **后端**: JDK 1.8
    - **前端**: Node.js, Vue-CLI, npm

## 系统截图

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin: 2rem 0;">
  <img src="/images/warehouse/2024-12-09 19_12_55_image.png" alt="截图1" style="width: 100%; border-radius: 8px;">
  <img src="/images/warehouse/2024-12-09 19_13_01_image.png" alt="截图2" style="width: 100%; border-radius: 8px;">
  <img src="/images/warehouse/2024-12-09 19_13_12_image.png" alt="截图3" style="width: 100%; border-radius: 8px;">
  <img src="/images/warehouse/2024-12-09 19_13_18_image.png" alt="截图4" style="width: 100%; border-radius: 8px;">
  <img src="/images/warehouse/2024-12-09 19_13_25_image.png" alt="截图5" style="width: 100%; border-radius: 8px;">
  <img src="/images/warehouse/2024-12-09 19_13_32_image.png" alt="截图6" style="width: 100%; border-radius: 8px;">
</div>
