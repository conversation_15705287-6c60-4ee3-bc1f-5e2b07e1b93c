# 图片资源说明

这个目录用于存放网站所需的图片资源。请按照以下结构组织图片文件：

## 📁 目录结构

```
public/images/
├── avatar.jpg              # 个人头像
├── hero-avatar.png         # 首页英雄区头像
├── logo.png               # 网站 Logo
├── favicon.ico            # 网站图标
├── social-card.png        # 社交媒体卡片图片
└── projects/              # 项目相关图片
    ├── ecommerce-admin.png
    ├── ecommerce-admin-1.png
    ├── ecommerce-admin-2.png
    ├── ecommerce-admin-3.png
    ├── ecommerce-admin-4.png
    ├── learning-platform.png
    ├── task-manager.png
    ├── blog-system.png
    ├── data-visualization.png
    └── wechat-mall.png
```

## 🖼️ 图片规格建议

### 个人头像
- **avatar.jpg**: 400x400px，正方形，用于关于页面
- **hero-avatar.png**: 300x300px，正方形，用于首页英雄区

### 网站标识
- **logo.png**: 120x40px，透明背景，用于导航栏
- **favicon.ico**: 32x32px，网站图标
- **social-card.png**: 1200x630px，用于社交媒体分享

### 项目截图
- **项目主图**: 800x450px (16:9 比例)，用于项目卡片展示
- **详情页截图**: 1200x675px (16:9 比例)，用于项目详情页面
- **格式**: PNG 或 JPG，建议使用 PNG 以获得更好的质量

## 📝 图片命名规范

1. **使用小写字母和连字符**: `project-name.png`
2. **项目相关图片**: `项目名-功能.png`，如 `ecommerce-admin-dashboard.png`
3. **避免使用空格和特殊字符**
4. **使用有意义的文件名**，便于管理和维护

## 🎨 图片优化建议

1. **压缩图片**: 使用工具如 TinyPNG 压缩图片大小
2. **选择合适格式**: 
   - 照片使用 JPG
   - 图标和透明图片使用 PNG
   - 简单图形可考虑 SVG
3. **响应式图片**: 为不同设备准备不同尺寸的图片
4. **懒加载**: 大图片建议使用懒加载技术

## 🔄 替换图片

要替换现有图片，请：

1. 准备符合规格的新图片
2. 使用相同的文件名替换原文件
3. 确保图片路径在代码中正确引用
4. 测试图片在不同设备上的显示效果

## 📱 响应式图片

对于需要在不同设备上显示的图片，建议准备多个尺寸：

- **桌面端**: 原始尺寸
- **平板端**: 75% 尺寸
- **移动端**: 50% 尺寸

## 🚀 性能优化

1. **WebP 格式**: 现代浏览器支持 WebP 格式，文件更小
2. **图片预加载**: 重要图片可以预加载
3. **CDN 加速**: 考虑使用 CDN 服务加速图片加载
4. **缓存策略**: 设置合适的缓存头

## 📄 版权说明

请确保使用的图片具有合法的使用权限：

- 自己拍摄的照片
- 免费商用图片（如 Unsplash、Pexels）
- 购买的正版图片
- 开源图片资源

避免使用有版权争议的图片。

---

**注意**: 这个 README.md 文件仅用于说明，实际部署时可以删除。
