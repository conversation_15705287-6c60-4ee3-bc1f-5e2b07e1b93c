# 服务能力

我们拥有专业的技术团队，掌握多种现代化技术栈，具备全栈开发能力。以下是我们的核心技术能力和服务专长领域。

## 🚀 前端开发

### 核心技术
<div class="skills-section">
  <div class="skills-category">
    <h3>JavaScript 框架</h3>
    <div class="skills-grid">
      <SkillTag name="Vue.js" level="expert" icon="🟢" show-level />
      <SkillTag name="React" level="intermediate" icon="⚛️" show-level />
      <SkillTag name="Angular" level="basic" icon="🔴" show-level />
      <SkillTag name="Nuxt.js" level="intermediate" icon="💚" show-level />
      <SkillTag name="Next.js" level="intermediate" icon="⚫" show-level />
    </div>
  </div>

  <div class="skills-category">
    <h3>编程语言</h3>
    <div class="skills-grid">
      <SkillTag name="JavaScript" level="expert" icon="🟨" show-level />
      <SkillTag name="TypeScript" level="intermediate" icon="🔷" show-level />
      <SkillTag name="HTML5" level="expert" icon="🧡" show-level />
      <SkillTag name="CSS3" level="expert" icon="🔵" show-level />
    </div>
  </div>

  <div class="skills-category">
    <h3>CSS 预处理器与框架</h3>
    <div class="skills-grid">
      <SkillTag name="Sass/SCSS" level="intermediate" icon="🌸" show-level />
      <SkillTag name="Less" level="intermediate" icon="🔵" show-level />
      <SkillTag name="Tailwind CSS" level="expert" icon="💙" show-level />
      <SkillTag name="Bootstrap" level="intermediate" icon="🟣" show-level />
    </div>
  </div>

  <div class="skills-category">
    <h3>UI 组件库</h3>
    <div class="skills-grid">
      <SkillTag name="Element Plus" level="expert" icon="🔷" show-level />
      <SkillTag name="Ant Design" level="intermediate" icon="🔵" show-level />
      <SkillTag name="Vuetify" level="intermediate" icon="💙" show-level />
      <SkillTag name="Vant" level="intermediate" icon="💚" show-level />
    </div>
  </div>
</div>

## 🔧 后端开发

<div class="skills-section">
  <div class="skills-category">
    <h3>服务端语言</h3>
    <div class="skills-grid">
      <SkillTag name="Node.js" level="intermediate" icon="💚" show-level />
      <SkillTag name="Python" level="intermediate" icon="🐍" show-level />
      <SkillTag name="Java" level="basic" icon="☕" show-level />
      <SkillTag name="PHP" level="basic" icon="🐘" show-level />
    </div>
  </div>

  <div class="skills-category">
    <h3>Web 框架</h3>
    <div class="skills-grid">
      <SkillTag name="Express.js" level="intermediate" icon="🚂" show-level />
      <SkillTag name="Koa.js" level="intermediate" icon="🥝" show-level />
      <SkillTag name="Django" level="basic" icon="🎸" show-level />
      <SkillTag name="Spring Boot" level="basic" icon="🍃" show-level />
    </div>
  </div>

  <div class="skills-category">
    <h3>数据库</h3>
    <div class="skills-grid">
      <SkillTag name="MongoDB" level="intermediate" icon="🍃" show-level />
      <SkillTag name="MySQL" level="intermediate" icon="🐬" show-level />
      <SkillTag name="PostgreSQL" level="basic" icon="🐘" show-level />
      <SkillTag name="Redis" level="basic" icon="🔴" show-level />
    </div>
  </div>
</div>

## 📱 移动端开发

<div class="skills-section">
  <div class="skills-category">
    <h3>跨平台开发</h3>
    <div class="skills-grid">
      <SkillTag name="微信小程序" level="intermediate" icon="💚" show-level />
      <SkillTag name="Vue Native" level="basic" icon="💚" show-level />
      <SkillTag name="React Native" level="basic" icon="⚛️" show-level />
      <SkillTag name="PWA" level="intermediate" icon="📱" show-level />
    </div>
  </div>
</div>

## 🛠️ 开发工具与环境

<div class="skills-section">
  <div class="skills-category">
    <h3>构建工具</h3>
    <div class="skills-grid">
      <SkillTag name="Vite" level="expert" icon="⚡" show-level />
      <SkillTag name="Webpack" level="intermediate" icon="📦" show-level />
      <SkillTag name="Rollup" level="basic" icon="📦" show-level />
      <SkillTag name="Parcel" level="basic" icon="📦" show-level />
    </div>
  </div>

  <div class="skills-category">
    <h3>版本控制</h3>
    <div class="skills-grid">
      <SkillTag name="Git" level="expert" icon="🌿" show-level />
      <SkillTag name="GitHub" level="expert" icon="🐙" show-level />
      <SkillTag name="GitLab" level="intermediate" icon="🦊" show-level />
    </div>
  </div>

  <div class="skills-category">
    <h3>开发环境</h3>
    <div class="skills-grid">
      <SkillTag name="VS Code" level="expert" icon="💙" show-level />
      <SkillTag name="WebStorm" level="intermediate" icon="🔷" show-level />
      <SkillTag name="Chrome DevTools" level="expert" icon="🔍" show-level />
    </div>
  </div>

  <div class="skills-category">
    <h3>包管理器</h3>
    <div class="skills-grid">
      <SkillTag name="npm" level="expert" icon="📦" show-level />
      <SkillTag name="yarn" level="intermediate" icon="🧶" show-level />
      <SkillTag name="pnpm" level="intermediate" icon="📦" show-level />
    </div>
  </div>
</div>

## 🎨 设计与用户体验

<div class="skills-section">
  <div class="skills-category">
    <h3>设计工具</h3>
    <div class="skills-grid">
      <SkillTag name="Figma" level="intermediate" icon="🎨" show-level />
      <SkillTag name="Adobe XD" level="basic" icon="🎨" show-level />
      <SkillTag name="Sketch" level="basic" icon="💎" show-level />
    </div>
  </div>

  <div class="skills-category">
    <h3>设计理念</h3>
    <div class="skills-grid">
      <SkillTag name="响应式设计" level="expert" icon="📱" show-level />
      <SkillTag name="用户体验设计" level="intermediate" icon="👤" show-level />
      <SkillTag name="无障碍设计" level="basic" icon="♿" show-level />
    </div>
  </div>
</div>

## 📊 数据可视化

<div class="skills-section">
  <div class="skills-category">
    <h3>图表库</h3>
    <div class="skills-grid">
      <SkillTag name="ECharts" level="intermediate" icon="📊" show-level />
      <SkillTag name="Chart.js" level="intermediate" icon="📈" show-level />
      <SkillTag name="D3.js" level="basic" icon="📊" show-level />
    </div>
  </div>
</div>

## 🚀 部署与运维

<div class="skills-section">
  <div class="skills-category">
    <h3>部署平台</h3>
    <div class="skills-grid">
      <SkillTag name="Netlify" level="intermediate" icon="🌐" show-level />
      <SkillTag name="Vercel" level="intermediate" icon="▲" show-level />
      <SkillTag name="GitHub Pages" level="expert" icon="📄" show-level />
      <SkillTag name="阿里云" level="basic" icon="☁️" show-level />
    </div>
  </div>

  <div class="skills-category">
    <h3>容器化</h3>
    <div class="skills-grid">
      <SkillTag name="Docker" level="basic" icon="🐳" show-level />
    </div>
  </div>
</div>

## 🎯 技术发展规划

- **微服务架构**: 提供企业级微服务架构设计和实施服务
- **云原生技术**: 深入云原生技术，提供容器化部署和运维服务
- **AI/ML 集成**: 探索人工智能在业务系统中的应用和集成
- **性能优化**: 提供专业的系统性能诊断和优化服务
- **新兴技术**: 持续关注和应用最新的技术趋势

---

<div style="text-align: center; margin: 3rem 0;">
  <h2>🌟 专业技术，优质服务</h2>
  <p style="color: var(--vp-c-text-2); font-size: 1.1rem; max-width: 600px; margin: 0 auto;">
    技术在不断发展，我们也在持续进步。每一项技能的掌握都是通过实际项目的锤炼而来，我们相信专业的技术能力是提供优质服务的基础。
  </p>
</div>
